import requests
import json
import argpar<PERSON>


def send_message(snapshot_path: str | None, log_tail: str | None = None, openai_user: str | None = None, snapshot: str | None = None):
    # Convert dictionary to JSON string
    payload_json = json.dumps(get_message(snapshot_path, log_tail, openai_user, snapshot))

    url = "https://microsoft.webhook.office.com/webhookb2/5eaa2376-6b7c-44a3-a917-cd16440e9cfa@72f988bf-86f1-41af-91ab-2d7cd011db47/IncomingWebhook/338babedd8654b7c9f71ce9855b32ab6/75c102a9-e447-4afc-a9ef-f34e34a4cd9b/V2fKsrx64-tknuaL8mFqGJklEbTy-OBJQRnXuzWReYbmk1"

    response = requests.post(url, data=payload_json, headers={"Content-Type": "application/json"})

    # Print response status for debugging
    print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        print("Message sent successfully!")
    else:
        print(f"Failed to send message: {response.text}")


def get_message(snapshot_path: str | None, log_tail: str | None = None, openai_user: str | None = None, snapshot: str | None = None):
    user_name = "Xiaolin Liu" if not openai_user else openai_user

    if snapshot_path:
        snapshot_name = snapshot_path.split("/")[-1]
        text = f"Hi <at>{user_name}</at>, the conversion is done successfully. The snapshot path is {snapshot_path}."
        text += f"\nPlease run ~/code/glass/personal/lixiaoli/mopub/publish.sh {snapshot_name} {openai_user} {snapshot} to create PR."
        text += "\nRemember to az login your corp account before running the script."
    else:
        text = f"Hi <at>{user_name}</at>, the conversion failed."
        if log_tail:
            text += f"\nLog tail: {log_tail}"
    return {
        "type": "message",
        "attachments": [
            {
                "contentType": "application/vnd.microsoft.card.adaptive",
                "content": {
                    "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                    "type": "AdaptiveCard",
                    "version": "1.2",
                    "body": [
                        {
                            "type": "TextBlock",
                            "text": text,
                            "wrap": True,
                        }
                    ],
                    "msteams": {
                        "entities": [
                            {
                                "type": "mention",
                                "text": f"<at>{user_name}</at>",
                                "mentioned": {
                                    "id": f"{user_name}@microsoft.com",
                                    "name": user_name,
                                },
                            }
                        ]
                    },
                },
            }
        ],
    }


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Send webhook message")
    parser.add_argument("snapshot_path", nargs="?", default=None, help="Snapshot path (optional)")
    parser.add_argument("--log_tail", type=str, help="Last line content of the retrieved log")
    parser.add_argument("--openai_user", type=str, help="User alias")
    parser.add_argument("--snapshot", type=str, help="Snapshot json name")
    args = parser.parse_args()
    send_message(args.snapshot_path, args.log_tail, args.openai_user, args.snapshot)
